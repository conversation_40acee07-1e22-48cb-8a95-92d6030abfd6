{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "seed": "knex seed:run --knexfile knexfile.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@heroicons/react": "^2.2.0", "axios": "^1.10.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.0", "qrcode": "^1.5.4", "slugify": "^1.6.6", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "stripe": "^18.1.0", "uuid": "^11.1.0"}, "devDependencies": {"knex": "^3.1.0"}}