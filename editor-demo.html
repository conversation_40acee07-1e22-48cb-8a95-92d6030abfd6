<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Menu Editor - Three Panel Layout Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .panel {
            min-height: calc(100vh - 120px);
        }
        .category-item, .menu-item {
            transition: all 0.3s ease;
        }
        .category-item:hover, .menu-item:hover {
            transform: translateX(4px);
        }
        .selected {
            background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
            border-color: #a855f7;
        }
        .drag-handle {
            cursor: grab;
        }
        .drag-handle:active {
            cursor: grabbing;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <button class="text-gray-600 hover:text-gray-800">← Back to Menus</button>
                <h1 class="text-2xl font-semibold text-gray-900">Edit Menu: Main Menu</h1>
            </div>
            <div class="flex items-center space-x-3">
                <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                    <option value="all">All</option>
                    <option value="active">Active</option>
                    <option value="passive">Passive</option>
                </select>
            </div>
        </div>
    </header>

    <div class="flex overflow-hidden">
        <!-- Left Panel: Categories & Filters -->
        <div class="w-1/4 bg-gray-50 border-r border-gray-200 panel overflow-y-auto">
            <div class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">Categories & Filters</h2>
                    <button class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm">
                        + Add
                    </button>
                </div>

                <div class="space-y-2">
                    <!-- Category 1 - Selected -->
                    <div class="category-item selected p-3 rounded-lg cursor-pointer border-2">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-2 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Appetizers</h3>
                                        <p class="text-sm text-gray-600 mt-1">Starters and small plates</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1 ml-2">
                                <span class="w-2 h-2 rounded-full bg-green-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>

                    <!-- Category 2 -->
                    <div class="category-item bg-white border-2 border-gray-200 hover:bg-gray-50 p-3 rounded-lg cursor-pointer">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-2 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Main Courses</h3>
                                        <p class="text-sm text-gray-600 mt-1">Hearty main dishes</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1 ml-2">
                                <span class="w-2 h-2 rounded-full bg-green-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>

                    <!-- Category 3 -->
                    <div class="category-item bg-white border-2 border-gray-200 hover:bg-gray-50 p-3 rounded-lg cursor-pointer">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-2 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Desserts</h3>
                                        <p class="text-sm text-gray-600 mt-1">Sweet endings</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1 ml-2">
                                <span class="w-2 h-2 rounded-full bg-green-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>

                    <!-- Category 4 -->
                    <div class="category-item bg-white border-2 border-gray-200 hover:bg-gray-50 p-3 rounded-lg cursor-pointer">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-2 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Beverages</h3>
                                        <p class="text-sm text-gray-600 mt-1">Drinks and refreshments</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1 ml-2">
                                <span class="w-2 h-2 rounded-full bg-gray-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Center Panel: Items in Selected Category -->
        <div class="flex-1 bg-white panel overflow-y-auto">
            <div class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">Items in Appetizers</h2>
                    <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm">
                        Add Item
                    </button>
                </div>

                <div class="space-y-3">
                    <!-- Menu Item 1 - Selected -->
                    <div class="menu-item selected p-4 rounded-lg cursor-pointer border-2">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-3 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Caesar Salad</h3>
                                        <p class="text-sm text-gray-600 mt-1">Fresh romaine lettuce with parmesan cheese and croutons</p>
                                        <p class="text-sm font-medium text-purple-600 mt-2">$12.99</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <span class="w-2 h-2 rounded-full bg-green-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Item 2 -->
                    <div class="menu-item bg-gray-50 border-2 border-gray-200 hover:bg-gray-100 p-4 rounded-lg cursor-pointer">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-3 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Bruschetta</h3>
                                        <p class="text-sm text-gray-600 mt-1">Toasted bread with fresh tomatoes, basil, and garlic</p>
                                        <p class="text-sm font-medium text-purple-600 mt-2">$8.99</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <span class="w-2 h-2 rounded-full bg-green-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Item 3 -->
                    <div class="menu-item bg-gray-50 border-2 border-gray-200 hover:bg-gray-100 p-4 rounded-lg cursor-pointer">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-3 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Mozzarella Sticks</h3>
                                        <p class="text-sm text-gray-600 mt-1">Crispy breaded mozzarella with marinara sauce</p>
                                        <p class="text-sm font-medium text-purple-600 mt-2">$9.99</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <span class="w-2 h-2 rounded-full bg-gray-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Item 4 -->
                    <div class="menu-item bg-gray-50 border-2 border-gray-200 hover:bg-gray-100 p-4 rounded-lg cursor-pointer">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="drag-handle mr-3 text-gray-400">⋮⋮</span>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Wings</h3>
                                        <p class="text-sm text-gray-600 mt-1">Buffalo wings with celery and blue cheese</p>
                                        <p class="text-sm font-medium text-purple-600 mt-2">$11.99</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                <span class="w-2 h-2 rounded-full bg-green-400"></span>
                                <button class="text-red-500 hover:text-red-700 text-xs">×</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel: Edit Form -->
        <div class="w-1/3 bg-gray-50 border-l border-gray-200 panel overflow-y-auto">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Edit Menu Item</h3>
                
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Item Name *</label>
                        <input
                            type="text"
                            value="Caesar Salad"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                            rows="3"
                        >Fresh romaine lettuce with parmesan cheese and croutons</textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Price *</label>
                        <input
                            type="number"
                            step="0.01"
                            value="12.99"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Image</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <div class="text-gray-500 text-sm">
                                <p>Drop image here or click to upload</p>
                                <p class="text-xs mt-1">PNG, JPG up to 5MB</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="is_available"
                            checked
                            class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                        <label for="is_available" class="ml-2 block text-sm text-gray-700">
                            Available to customers
                        </label>
                    </div>
                    
                    <div class="border-t pt-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Multi-language Support</h4>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Name (English)</label>
                                <input
                                    type="text"
                                    value="Caesar Salad"
                                    class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                                />
                            </div>
                            
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Name (Turkish)</label>
                                <input
                                    type="text"
                                    value="Sezar Salatası"
                                    class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                                />
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3 pt-4">
                        <button
                            type="button"
                            class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg font-medium transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            class="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
                        >
                            Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Demo interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Category selection
            const categories = document.querySelectorAll('.category-item');
            categories.forEach(category => {
                category.addEventListener('click', function() {
                    categories.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // Menu item selection
            const items = document.querySelectorAll('.menu-item');
            items.forEach(item => {
                item.addEventListener('click', function() {
                    items.forEach(i => i.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });
        });
    </script>
</body>
</html>
