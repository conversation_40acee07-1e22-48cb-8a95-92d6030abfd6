{"name": "qr-menu-platform", "version": "1.0.0", "private": false, "homepage": ".", "dependencies": {"@heroicons/react": "^2.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "ajv": "^8.0.0", "axios": "^1.8.4", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "qr-code-styling": "^1.9.2", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.3", "react-router-dom": "^6.22.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "init-tailwind": "tailwindcss init -p"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@tailwindcss/postcss": "^4.1.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}