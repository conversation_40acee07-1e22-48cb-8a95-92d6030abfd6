/* Design Customization Page Styles */
.design-customization-page {
  min-height: 100vh;
  background-color: #f8fafc;
  padding: 20px;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* Progress Indicator */
.progress-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 40px;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #94a3b8;
  font-weight: 500;
}

.progress-step.completed {
  color: #10b981;
}

.progress-step.active {
  color: #8b5cf6;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.progress-step.completed .step-number {
  background-color: #10b981;
  color: white;
}

.progress-step.active .step-number {
  background-color: #8b5cf6;
  color: white;
}

/* Main Content Layout */
.customization-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
}

/* Left Column - Customization Controls */
.customization-controls {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.controls-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
}

.controls-header p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 40px;
}

/* Customization Sections */
.customization-section {
  margin-bottom: 40px;
}

.customization-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 16px;
}

/* Logo Upload */
.logo-upload-area {
  margin-bottom: 8px;
}

.upload-logo-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 120px;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
}

.upload-logo-btn:hover {
  border-color: #8b5cf6;
  color: #8b5cf6;
  background: #faf5ff;
}

.upload-icon {
  width: 24px;
  height: 24px;
}

.logo-preview {
  position: relative;
  width: 200px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
}

.logo-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8fafc;
}

.remove-logo-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s;
}

.remove-logo-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.file-hint {
  font-size: 12px;
  color: #94a3b8;
  margin: 0;
}

/* Color Controls */
.color-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.color-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-control label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.color-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  padding: 0;
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  border: 2px solid #e2e8f0;
  flex-shrink: 0;
}

.color-value {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
}

/* Navigation Controls */
.navigation-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e2e8f0;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.skip-link {
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
  transition: color 0.2s;
}

.skip-link:hover {
  color: #374151;
}

.next-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.next-btn:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

/* Background Upload Styles */
.background-upload-area {
  margin-bottom: 20px;
}

.background-upload-area h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.background-preview {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
}

.background-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-background-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
}

.upload-background-btn {
  width: 100%;
  height: 120px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.upload-background-btn:hover {
  border-color: #8b5cf6;
  background: #f3f4f6;
}

.upload-background-btn .upload-icon {
  width: 24px;
  height: 24px;
  color: #6b7280;
}

.upload-background-btn span {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Background Settings */
.background-settings {
  margin-top: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.setting-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
}

.opacity-slider {
  width: 100%;
  margin: 8px 0;
}

.overlay-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.overlay-controls .opacity-slider {
  flex: 1;
  margin: 0;
}

.opacity-label {
  font-size: 12px;
  color: #6b7280;
  min-width: 35px;
}

/* Theme Gallery */
.theme-gallery {
  margin-top: 24px;
}

.theme-gallery h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.theme-swatch {
  height: 60px;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: end;
  padding: 8px;
}

.theme-swatch:hover {
  border-color: #8b5cf6;
  transform: translateY(-2px);
}

.theme-swatch.selected {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.theme-name {
  font-size: 10px;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 6px;
  border-radius: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Right Column - Mobile Preview */
.mobile-preview {
  position: sticky;
  top: 20px;
  height: fit-content;
}

.phone-frame {
  width: 375px;
  height: 667px;
  background: #1e293b;
  border-radius: 25px;
  padding: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.phone-header {
  background: #1e293b;
  border-radius: 17px 17px 0 0;
  padding: 8px 16px;
  color: white;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.time {
  font-size: 16px;
}

.status-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.status-icon {
  width: 16px;
  height: 16px;
  color: white;
}

.phone-content {
  height: calc(100% - 40px);
  border-radius: 0 0 17px 17px;
  padding: 30px 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

/* Preview Logo */
.preview-logo-section {
  width: 120px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-logo-placeholder {
  width: 100%;
  height: 100%;
  background: #f1f5f9;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  width: 32px;
  height: 32px;
  color: #94a3b8;
}

/* Preview Restaurant Name */
.preview-restaurant-name h2 {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  text-align: center;
}

/* Preview Language Selector */
.preview-language-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
}

.dropdown-icon {
  width: 16px;
  height: 16px;
}

/* Preview Menu Button */
.preview-menu-button {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin: 10px 0;
}

.preview-menu-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Preview Menu Links */
.preview-menu-links {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.link-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .customization-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .mobile-preview {
    position: relative;
    display: flex;
    justify-content: center;
    order: -1; /* Show preview first on mobile */
  }
}

@media (max-width: 768px) {
  .design-customization-page {
    padding: 12px;
  }

  .customization-controls {
    padding: 20px;
  }

  .phone-frame {
    width: 100%;
    max-width: 375px;
  }

  .navigation-controls {
    flex-direction: column;
    gap: 16px;
  }

  .right-controls {
    width: 100%;
    justify-content: space-between;
    flex-direction: column;
    gap: 12px;
  }

  .back-btn,
  .next-btn {
    min-height: 44px;
  }

  .skip-link {
    text-align: center;
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .design-customization-page {
    padding: 8px;
  }

  .customization-controls {
    padding: 16px;
  }

  .controls-header h1 {
    font-size: 24px;
  }

  .phone-frame {
    max-width: 320px;
  }

  .color-controls {
    gap: 16px;
  }
}

/* Card Style Controls */
.card-style-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.border-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.border-slider {
  flex: 1;
}

.border-style-select {
  min-width: 80px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
}

.color-picker.small {
  width: 32px;
  height: 32px;
}

.radius-slider {
  width: 100%;
  margin: 8px 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #8b5cf6;
}

/* Preview Menu Cards */
.preview-menu-cards {
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-menu-card {
  padding: 16px;
  margin: 0 20px;
}

.preview-menu-card h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.preview-menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.preview-menu-item:last-child {
  margin-bottom: 0;
}
