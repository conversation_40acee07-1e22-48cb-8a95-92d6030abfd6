import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import '../styles/Auth.css';

const Login = () => {
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            const response = await axios.post('/api/auth/login', formData);

            console.log('🔍 [Login.js] Login response:', response.data);

            // CRITICAL: Use consistent storage keys with AuthContext
            localStorage.setItem('authToken', response.data.token);
            localStorage.setItem('authUser', JSON.stringify(response.data.user));

            console.log('🔍 [Login.js] Stored user data:', response.data.user);
            console.log('🔍 [Login.js] User restaurant_id:', response.data.user.restaurant_id);

            // Set auth header for future requests
            axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

            navigate('/dashboard');
        } catch (error) {
            console.error('Login error:', error);
            setError(
                error.response?.data?.error ||
                'Login failed. Please check your credentials and ensure the server is running.'
            );
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="auth-container">
            <div className="auth-card">
                <h2>Login to QR Menu</h2>
                {error && <div className="error-message">{error}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="form-group">
                        <label htmlFor="email">Email</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            required
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="password">Password</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            value={formData.password}
                            onChange={handleChange}
                            required
                        />
                    </div>
                    <button 
                        type="submit" 
                        className="auth-button"
                        disabled={loading}
                    >
                        {loading ? 'Logging in...' : 'Login'}
                    </button>
                </form>
                <p className="auth-link">
                    Don't have an account? <Link to="/register">Register</Link>
                </p>
            </div>
        </div>
    );
};

export default Login; 