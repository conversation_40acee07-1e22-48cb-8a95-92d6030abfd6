/* Menu Creation Page Styles */
.menu-creation-page {
  min-height: 100vh;
  background-color: #f8fafc;
  padding: 20px;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* Progress Indicator */
.progress-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 40px;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #94a3b8;
  font-weight: 500;
}

.progress-step.active {
  color: #8b5cf6;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.progress-step.active .step-number {
  background-color: #8b5cf6;
  color: white;
}

/* Main Content Layout */
.menu-creation-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
}

/* Left Column - Menu Editor */
.menu-editor {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 0;
  overflow: hidden;
}

.editor-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
}

.editor-header p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 30px;
}

.editor-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.language-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.language-selector label {
  font-weight: 500;
  color: #374151;
}

.language-dropdown {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.automatic-create-btn {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.automatic-create-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.example-hint {
  background: #f1f5f9;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #64748b;
  margin-bottom: 30px;
}

/* Sections Container */
.sections-container {
  margin-bottom: 20px;
}

.section-editor {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.2s;
}

.section-editor.dragging {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: rotate(2deg);
}

/* Multi-language input integration */
.section-inputs .multi-language-input {
  margin-bottom: 12px;
}

.section-inputs .multi-language-input:last-child {
  margin-bottom: 0;
}

.item-inputs .multi-language-input {
  margin-bottom: 8px;
}

.item-inputs .multi-language-input:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: grab;
  gap: 12px;
}

.section-header:active {
  cursor: grabbing;
}

/* Section Image Upload */
.section-image-upload {
  flex-shrink: 0;
}

.section-image-preview {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
}

.section-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.remove-image-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.upload-image-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 11px;
  gap: 2px;
}

.upload-image-btn:hover {
  border-color: #8b5cf6;
  color: #8b5cf6;
  background: #faf5ff;
}

.upload-image-btn.small {
  width: 40px;
  height: 40px;
  border-radius: 6px;
}

.upload-image-btn.small span {
  display: none;
}

.section-inputs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title-input {
  font-size: 16px;
  font-weight: 600;
  border: none;
  background: transparent;
  color: #1e293b;
  padding: 4px 0;
}

.section-title-input:focus {
  outline: none;
  border-bottom: 2px solid #8b5cf6;
}

.section-description-input {
  font-size: 14px;
  border: none;
  background: transparent;
  color: #64748b;
  padding: 4px 0;
}

.section-description-input:focus {
  outline: none;
  border-bottom: 2px solid #8b5cf6;
}

.section-controls {
  display: flex;
  gap: 8px;
}

.toggle-btn, .delete-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s;
}

.toggle-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.delete-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* Section Items */
.section-items {
  padding: 0 16px 16px;
}

.items-container {
  margin-bottom: 12px;
}

.item-editor {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid #e2e8f0;
  cursor: grab;
}

.item-editor.dragging {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-editor:active {
  cursor: grabbing;
}

.item-inputs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-title-input {
  font-weight: 500;
  border: none;
  background: transparent;
  color: #1e293b;
  font-size: 14px;
}

.item-title-input:focus {
  outline: none;
  border-bottom: 1px solid #8b5cf6;
}

.item-description-input {
  font-size: 12px;
  border: none;
  background: transparent;
  color: #64748b;
}

.item-description-input:focus {
  outline: none;
  border-bottom: 1px solid #8b5cf6;
}

.item-price-container {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f1f5f9;
  padding: 6px 8px;
  border-radius: 4px;
}

.currency {
  font-weight: 600;
  color: #8b5cf6;
}

.item-price-input {
  width: 60px;
  border: none;
  background: transparent;
  font-weight: 500;
  text-align: right;
}

.item-price-input:focus {
  outline: none;
}

/* Item Image Upload */
.item-image-upload {
  flex-shrink: 0;
}

.item-image-preview {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
}

.item-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-item-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s;
}

.delete-item-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

.add-item-btn, .add-section-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  color: #64748b;
  font-weight: 500;
  transition: all 0.2s;
  width: 100%;
  justify-content: center;
}

.add-item-btn:hover, .add-section-btn:hover {
  border-color: #8b5cf6;
  color: #8b5cf6;
  background: #faf5ff;
}

.navigation-buttons {
  display: flex;
  gap: 12px;
  margin-top: 30px;
}

.back-btn {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.next-btn {
  background: #8b5cf6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
}

.next-btn:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

/* Right Column - Mobile Preview */
.mobile-preview {
  position: sticky;
  top: 20px;
  height: fit-content;
}

.phone-frame {
  width: 375px;
  height: 667px;
  background: #1e293b;
  border-radius: 25px;
  padding: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.phone-header {
  background: #8b5cf6;
  border-radius: 17px 17px 0 0;
  padding: 8px 16px;
  color: white;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.time {
  font-size: 16px;
}

.status-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.signal-bars, .wifi-icon, .battery-icon {
  width: 16px;
  height: 10px;
  background: white;
  border-radius: 2px;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.restaurant-name {
  font-size: 18px;
  font-weight: 700;
}

.menu-icon {
  width: 24px;
  height: 24px;
}

.phone-content {
  background: white;
  height: calc(100% - 60px);
  border-radius: 0 0 17px 17px;
  padding: 20px;
  overflow-y: auto;
}

.menu-title {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  color: #1e293b;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #f1f5f9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 20px;
  gap: 8px;
}

.search-icon, .filter-icon {
  width: 20px;
  height: 20px;
  color: #64748b;
}

.search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
}

.search-bar input:focus {
  outline: none;
}

.category-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.category-tab {
  background: #e2e8f0;
  color: #64748b;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s;
}

.category-tab.active {
  background: #8b5cf6;
  color: white;
}

.category-tab:hover:not(.active) {
  background: #cbd5e1;
}

.menu-content {
  padding-bottom: 20px;
}

.preview-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.section-image-container {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.section-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.section-image-placeholder {
  width: 100%;
  height: 100%;
  background: #e2e8f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.placeholder-icon {
  width: 24px;
  height: 24px;
  color: #94a3b8;
}

.section-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.section-info p {
  font-size: 14px;
  color: #64748b;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.item-info h4 {
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 4px;
}

.item-info p {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.item-price {
  font-size: 16px;
  font-weight: 600;
  color: #8b5cf6;
}

.item-image-container {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-image-placeholder {
  width: 100%;
  height: 100%;
  background: #e2e8f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .menu-creation-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .mobile-preview {
    position: relative;
    display: flex;
    justify-content: center;
    order: -1; /* Show preview first on mobile */
  }
}

@media (max-width: 768px) {
  .menu-creation-page {
    padding: 12px;
  }

  .menu-editor {
    padding: 20px;
  }

  .phone-frame {
    width: 100%;
    max-width: 375px;
  }

  .navigation-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .back-btn,
  .next-btn {
    min-height: 44px;
  }
}

@media (max-width: 480px) {
  .menu-creation-page {
    padding: 8px;
  }

  .menu-editor {
    padding: 16px;
  }

  .editor-header h1 {
    font-size: 24px;
  }

  .phone-frame {
    max-width: 320px;
  }
}
