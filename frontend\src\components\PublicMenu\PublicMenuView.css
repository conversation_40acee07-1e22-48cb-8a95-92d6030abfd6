/* Modern Mobile-First Public Menu View */
.public-menu-view {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
  line-height: 1.5;
  color: var(--text-color, #1f2937);
  background: var(--bg-color, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
  overflow-x: hidden;
  box-sizing: border-box;
  position: relative;
}

/* Modern Loading State */
.public-menu-loading {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.public-menu-loading p {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

/* Unavailable State */
.public-menu-unavailable {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  padding: 20px;
}

.unavailable-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.unavailable-icon {
  width: 64px;
  height: 64px;
  color: #f59e0b;
  margin: 0 auto 24px;
}

.unavailable-content h2 {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.unavailable-content p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.unavailable-content p:last-child {
  margin-bottom: 0;
}

/* Modern Mobile Header */
.menu-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  padding: 20px;
  max-width: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
}

.restaurant-logo {
  position: relative;
  margin-bottom: 8px;
}

.restaurant-logo img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.logo-placeholder {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 3px dashed #cbd5e1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: #64748b;
}

.restaurant-info {
  width: 100%;
  text-align: center;
}

.restaurant-name {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 12px 0;
  color: #1e293b;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.restaurant-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  color: #475569;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.7);
  padding: 6px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.detail-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  color: #6366f1;
}

.language-selector {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.language-dropdown {
  appearance: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 8px 32px 8px 12px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  min-width: 80px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.language-dropdown:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.dropdown-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #6b7280;
  pointer-events: none;
}

/* Modern Search Section */
.search-section {
  padding: 20px;
  background: transparent;
  margin-top: -10px;
}

.search-container {
  max-width: 100%;
  margin: 0;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #6b7280;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 16px 20px 16px 50px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  color: #1f2937;
  font-weight: 500;
}

.search-input:focus {
  outline: none;
  transform: scale(1.02);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  background: white;
}

.search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.clear-search {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: #f3f4f6;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: #e5e7eb;
  transform: translateY(-50%) scale(1.1);
}

/* Modern Category Navigation */
.category-navigation {
  background: transparent;
  padding: 0 20px 20px 20px;
  z-index: 90;
}

.category-tabs {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 8px 0;
  scroll-behavior: smooth;
}

.category-tabs::-webkit-scrollbar {
  display: none;
}

.section-tab {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: none;
  color: #374151;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.section-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.section-tab:hover::before {
  left: 100%;
}

.section-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

.section-tab.active {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.4);
}

/* Modern Menu Content */
.menu-content {
  max-width: 100%;
  margin: 0;
  padding: 0 20px 40px 20px;
  box-sizing: border-box;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 30px 30px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 1;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
}

/* Search Results */
.search-results {
  margin-bottom: 30px;
  padding-top: 30px;
}

.search-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 20px 0;
  color: #1e293b;
  text-align: center;
}

.search-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

/* Menu Sections */
.menu-sections {
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding-top: 30px;
}

.menu-section {
  scroll-margin-top: 200px;
}

.section-header {
  margin-bottom: 24px;
  text-align: center;
}

.section-image {
  width: 100%;
  height: 180px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.section-title {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 12px 0;
  color: #1e293b;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 24px 0;
  line-height: 1.6;
  font-weight: 500;
}

.section-items {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

/* Modern Menu Item Card */
.menu-item-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 20px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.menu-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-item-card:hover::before {
  opacity: 1;
}

.menu-item-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.item-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-section-tag {
  display: inline-block;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  font-size: 11px;
  font-weight: 700;
  padding: 6px 12px;
  border-radius: 15px;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 10px rgba(99, 102, 241, 0.3);
}

.item-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 10px 0;
  color: #1e293b;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.item-description {
  font-size: 15px;
  color: #64748b;
  margin: 0 0 16px 0;
  line-height: 1.5;
  font-weight: 400;
}

.item-price {
  font-size: 22px;
  font-weight: 800;
  color: #059669;
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.item-image-container {
  flex-shrink: 0;
  width: 100px;
  height: 100px;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.item-image-container:hover .item-image {
  transform: scale(1.1);
}

.item-image-placeholder {
  width: 100%;
  height: 100%;
  background: #f1f5f9;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  width: 24px;
  height: 24px;
  color: #94a3b8;
}

/* Footer */
.menu-footer {
  text-align: center;
  padding: 40px 20px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 40px;
}

.menu-footer p {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .restaurant-info {
    width: 100%;
  }

  .restaurant-details {
    flex-direction: column;
    gap: 8px;
  }

  .language-selector {
    top: 15px;
    right: 15px;
  }

  .restaurant-name {
    font-size: 24px;
  }

  .menu-section {
    scroll-margin-top: 200px;
  }

  .section-title {
    font-size: 24px;
  }

  .item-content {
    gap: 16px;
  }

  .item-image-container {
    width: 90px;
    height: 90px;
  }

  .item-title {
    font-size: 18px;
  }

  .item-price {
    font-size: 20px;
  }

  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 14px 20px 14px 50px;
  }

  .section-items {
    gap: 16px;
  }

  .menu-item-card {
    padding: 18px;
  }
}

@media (max-width: 480px) {
  .menu-content {
    padding: 0 16px 40px 16px;
    border-radius: 25px 25px 0 0;
  }

  .search-section {
    padding: 16px;
  }

  .category-tabs {
    padding: 0 16px;
    gap: 10px;
  }

  .menu-item-card {
    padding: 16px;
    border-radius: 16px;
  }

  .section-image {
    height: 140px;
    border-radius: 16px;
  }

  .restaurant-name {
    font-size: 22px;
  }

  .detail-item {
    font-size: 14px;
    padding: 5px 10px;
  }

  .section-tab {
    padding: 10px 16px;
    font-size: 13px;
  }

  .item-title {
    font-size: 15px;
  }

  .item-description {
    font-size: 13px;
  }

  .item-price {
    font-size: 15px;
  }
}
