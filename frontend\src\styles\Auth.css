.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    background-color: #f5f5f5;
}

.auth-card {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.auth-card h2 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.form-group input:focus {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.auth-button {
    width: 100%;
    padding: 12px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.auth-button:hover {
    background-color: #2a6fbf;
}

.auth-button:disabled {
    background-color: #a0c3e8;
    cursor: not-allowed;
}

.error-message {
    margin-bottom: 20px;
    padding: 10px 15px;
    background-color: #f8d7da;
    color: #721c24;
    border-radius: 4px;
    font-size: 14px;
}

.auth-link {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #6c757d;
}

.auth-link a {
    color: #4a90e2;
    text-decoration: none;
    font-weight: 500;
}

.auth-link a:hover {
    text-decoration: underline;
} 