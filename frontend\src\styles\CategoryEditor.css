.add-category-button {
    width: 100%;
    padding: 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease;
}

.add-category-button:hover {
    background-color: #45a049;
}

.category-editor {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 20px;
}

.category-editor input,
.category-editor textarea {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.2s ease;
}

.category-editor input:focus,
.category-editor textarea:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.category-editor textarea {
    min-height: 80px;
    resize: vertical;
}

.category-editor-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.category-editor-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.category-editor-actions button[type="submit"] {
    background-color: #007bff;
    color: white;
}

.category-editor-actions button[type="submit"]:hover {
    background-color: #0069d9;
}

.category-editor-actions button[type="button"] {
    background-color: #6c757d;
    color: white;
}

.category-editor-actions button[type="button"]:hover {
    background-color: #5a6268;
}

/* Responsive Design */
@media (max-width: 576px) {
    .category-editor {
        padding: 10px;
    }

    .category-editor-actions {
        flex-direction: column;
    }

    .category-editor-actions button {
        width: 100%;
    }
} 