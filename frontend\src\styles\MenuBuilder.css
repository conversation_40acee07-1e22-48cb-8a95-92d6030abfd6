.menu-builder {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 20px;
    padding: 20px;
    height: calc(100vh - 60px);
    background-color: #f8f9fa;
}

.sidebar, .main-content, .template-sidebar {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.sidebar, .template-sidebar {
    gap: 20px;
}

.main-content {
    gap: 15px;
}

.sidebar h2, .main-content h2, .template-sidebar h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.4rem;
    position: relative;
    padding-bottom: 10px;
}

.sidebar h2::after, .main-content h2::after, .template-sidebar h2::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40px;
    height: 3px;
    background-color: #4a90e2;
}

.template-sidebar h3 {
    font-size: 1.1rem;
    color: #2c3e50;
    margin: 0 0 12px 0;
}

.category-list {
    flex: 1;
    overflow-y: auto;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    margin-bottom: 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.category-item:hover {
    background-color: #e9ecef;
    transform: translateX(2px);
}

.category-item.selected {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.category-actions {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.category-item:hover .category-actions {
    opacity: 1;
}

.category-actions button {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
}

.category-actions button:hover {
    background-color: #f8d7da;
}

.no-category-selected {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #6c757d;
    font-size: 1.1rem;
    text-align: center;
    padding: 20px;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 1.2rem;
    color: #6c757d;
}

.qr-code-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.qr-code-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-top: 10px;
}

.download-qr-button {
    width: 100%;
    padding: 10px 15px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.download-qr-button:hover {
    background-color: #1976d2;
}

.menu-preview {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.preview-container {
    border-radius: 8px;
    overflow: hidden;
    height: 480px;
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar,
.main-content::-webkit-scrollbar,
.template-sidebar::-webkit-scrollbar,
.category-list::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track,
.template-sidebar::-webkit-scrollbar-track,
.category-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb,
.template-sidebar::-webkit-scrollbar-thumb,
.category-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover,
.template-sidebar::-webkit-scrollbar-thumb:hover,
.category-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .menu-builder {
        grid-template-columns: 220px 1fr 280px;
    }
}

@media (max-width: 992px) {
    .menu-builder {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
        height: auto;
        gap: 15px;
    }

    .sidebar, .main-content, .template-sidebar {
        height: auto;
        max-height: none;
    }

    .sidebar, .template-sidebar {
        max-height: 400px;
    }
}

@media (max-width: 576px) {
    .menu-builder {
        padding: 10px;
        gap: 10px;
    }

    .sidebar, .main-content, .template-sidebar {
        padding: 15px;
    }
} 