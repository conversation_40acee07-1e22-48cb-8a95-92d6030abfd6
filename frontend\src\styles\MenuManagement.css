/* Enhanced Menu Management Page Styles */

.menu-management-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 3rem 2rem;
  margin-bottom: 3rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 3rem;
  position: relative;
  z-index: 1;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.hero-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: #fbbf24;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.hero-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.stat-icon {
  width: 1rem;
  height: 1rem;
  opacity: 0.8;
}

/* Enhanced Status Card */
.enhanced-status-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  padding: 2rem;
  min-width: 350px;
  flex-shrink: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.status-header {
  margin-bottom: 1.5rem;
}

.status-indicator-enhanced {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-icon-enhanced {
  width: 2rem;
  height: 2rem;
  padding: 0.5rem;
  border-radius: 12px;
  flex-shrink: 0;
}

.status-indicator-enhanced.active .status-icon-enhanced {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.status-indicator-enhanced:not(.active) .status-icon-enhanced {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.status-text-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-title {
  font-weight: 700;
  font-size: 1.125rem;
  color: #1f2937;
}

.status-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.status-actions {
  display: flex;
  justify-content: center;
}

.status-toggle-enhanced {
  padding: 1rem 2rem;
  border-radius: 16px;
  border: none;
  font-weight: 700;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
  min-width: 160px;
  justify-content: center;
}

.status-toggle-enhanced.active {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  box-shadow: 0 4px 16px rgba(251, 191, 36, 0.4);
}

.status-toggle-enhanced:not(.active) {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

.status-toggle-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.status-toggle-enhanced:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner-small {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

/* Enhanced Cards Grid */
.enhanced-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  align-items: stretch;
}

/* Enhanced Card Base */
.enhanced-card {
  background: white;
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.enhanced-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 0, 0, 0.1);
}

.enhanced-card:hover .card-glow {
  opacity: 1;
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.primary-card .card-glow {
  background: linear-gradient(90deg, #8b5cf6, #3b82f6);
}

.design-card .design-glow {
  background: linear-gradient(90deg, #f59e0b, #ef4444);
}

.share-card .share-glow {
  background: linear-gradient(90deg, #10b981, #06b6d4);
}

/* Card Header Enhanced */
.card-header-enhanced {
  padding: 2rem 2rem 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.card-icon-enhanced {
  width: 4rem;
  height: 4rem;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.card-icon-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  border-radius: inherit;
}

.card-icon-enhanced.edit-icon {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
}

.card-icon-enhanced.design-icon {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.card-icon-enhanced.share-icon {
  background: linear-gradient(135deg, #10b981, #06b6d4);
}

.icon-enhanced {
  width: 2rem;
  height: 2rem;
  color: white;
  position: relative;
  z-index: 1;
}

.card-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: white;
}

.primary-card .card-badge {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
}

.design-card .design-badge {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.share-card .share-badge {
  background: linear-gradient(135deg, #10b981, #06b6d4);
}

/* Card Content Enhanced */
.card-content-enhanced {
  padding: 0 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title-enhanced {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.card-description-enhanced {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

/* Card Metrics */
.card-metrics {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
}

.metric-item {
  text-align: center;
  flex: 1;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 900;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

.metric-divider {
  width: 1px;
  height: 3rem;
  background: linear-gradient(to bottom, transparent, #e2e8f0, transparent);
}

/* Design Preview Enhanced */
.design-preview-enhanced {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
}

.color-palette {
  margin-bottom: 1rem;
}

.palette-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.color-swatches-enhanced {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.color-swatch-enhanced {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 3px solid white;
  position: relative;
  transition: transform 0.3s ease;
}

.color-swatch-enhanced:hover {
  transform: scale(1.1);
}

.design-features {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Share Content Enhanced */
.share-content-enhanced {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 2rem;
}

.qr-section-enhanced {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  border: 1px solid #e2e8f0;
  position: relative;
}

.qr-container-enhanced {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.qr-code-wrapper {
  padding: 1rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  position: relative;
}

.qr-overlay {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #10b981, #06b6d4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.qr-overlay-icon {
  width: 1rem;
  height: 1rem;
  color: white;
}

.qr-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.qr-action-btn {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: none;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.qr-action-btn.download {
  color: #3b82f6;
}

.qr-action-btn.preview {
  color: #10b981;
}

.qr-action-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Link Section Enhanced */
.link-section-enhanced {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.link-display {
  margin-bottom: 1.5rem;
}

.link-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.link-header-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
}

.link-label-enhanced {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.link-container-enhanced {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
}

.link-url-enhanced {
  flex: 1;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #374151;
  background: #f8fafc;
  padding: 0.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.copy-button-enhanced {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  border: none;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-button-enhanced:hover {
  background: #e2e8f0;
}

.copy-button-enhanced.success {
  background: #d1fae5;
  border-color: #10b981;
}

.copy-icon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
}

.copy-button-enhanced.success .copy-icon {
  color: #10b981;
}

.share-stats {
  display: flex;
  gap: 2rem;
  justify-content: center;
}

.share-stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 900;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-text {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

/* Enhanced Button Styles */
.card-footer-enhanced {
  padding: 0 2rem 2rem 2rem;
  margin-top: auto;
}

.action-button-enhanced {
  width: 100%;
  padding: 1rem 2rem;
  border-radius: 16px;
  border: none;
  font-weight: 700;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.action-button-enhanced:hover {
  transform: translateY(-2px);
}

.action-button-enhanced.primary {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
  color: white;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.action-button-enhanced.primary:hover {
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.6);
}

.action-button-enhanced.secondary {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  color: white;
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
}

.action-button-enhanced.secondary:hover {
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.6);
}

.action-button-enhanced.tertiary {
  background: linear-gradient(135deg, #10b981, #06b6d4);
  color: white;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

.action-button-enhanced.tertiary:hover {
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.6);
}

.action-button-enhanced.full-width {
  width: 100%;
}

.button-icon-enhanced {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.button-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.action-button-enhanced:hover .button-shine {
  left: 100%;
}

/* Analytics Dashboard */
.analytics-dashboard {
  background: white;
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-header {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.dashboard-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #8b5cf6;
}

.dashboard-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.analytics-card {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.analytics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.analytics-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.analytics-icon.restaurant {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
}

.analytics-icon.update {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.analytics-icon.categories {
  background: linear-gradient(135deg, #10b981, #06b6d4);
}

.analytics-icon.products {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
}

.analytics-icon-svg {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.analytics-content {
  flex: 1;
  min-width: 0;
}

.analytics-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.analytics-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

/* Animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhanced-card {
  animation: fadeInUp 0.6s ease-out;
}

.enhanced-card:nth-child(1) {
  animation-delay: 0.1s;
}

.enhanced-card:nth-child(2) {
  animation-delay: 0.2s;
}

.enhanced-card:nth-child(3) {
  animation-delay: 0.3s;
}

/* Loading States */
.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #8b5cf6;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .enhanced-cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .menu-management-content {
    padding: 0 1rem;
  }

  .hero-section {
    padding: 2rem 1.5rem;
    margin-bottom: 2rem;
  }

  .hero-content {
    flex-direction: column;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    justify-content: center;
  }

  .enhanced-status-card {
    min-width: auto;
    width: 100%;
  }

  .enhanced-cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .card-header-enhanced {
    padding: 1.5rem 1.5rem 0 1.5rem;
  }

  .card-content-enhanced {
    padding: 0 1.5rem;
  }

  .card-footer-enhanced {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }

  .card-metrics {
    flex-direction: column;
    gap: 1rem;
  }

  .metric-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #e2e8f0, transparent);
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .analytics-card {
    padding: 1rem;
  }

  .qr-actions {
    gap: 0.75rem;
  }

  .share-stats {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 1.5rem 1rem;
  }

  .hero-title {
    font-size: 1.75rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .hero-icon {
    width: 2rem;
    height: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 0.75rem;
  }

  .stat-badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .enhanced-status-card {
    padding: 1.5rem;
  }

  .status-toggle-enhanced {
    padding: 0.875rem 1.5rem;
    font-size: 0.8rem;
    min-width: 140px;
  }

  .card-title-enhanced {
    font-size: 1.25rem;
  }

  .card-description-enhanced {
    font-size: 0.875rem;
  }

  .metric-value {
    font-size: 2rem;
  }

  .analytics-dashboard {
    padding: 1.5rem;
  }

  .dashboard-title {
    font-size: 1.25rem;
  }

  .analytics-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .analytics-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .qr-container-enhanced {
    margin-bottom: 1rem;
  }

  .qr-code-wrapper {
    padding: 0.75rem;
  }

  .link-container-enhanced {
    flex-direction: column;
    gap: 0.75rem;
  }

  .copy-button-enhanced {
    width: 100%;
    height: 2.5rem;
    border-radius: 8px;
  }
}

/* Focus States for Accessibility */
.action-button-enhanced:focus,
.status-toggle-enhanced:focus,
.qr-action-btn:focus,
.copy-button-enhanced:focus {
  outline: none;
  ring: 2px;
  ring-offset: 2px;
  ring-color: #8b5cf6;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .enhanced-card {
    border: 2px solid #000;
  }

  .card-glow {
    display: none;
  }

  .action-button-enhanced {
    border: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .enhanced-card,
  .action-button-enhanced,
  .analytics-card,
  .qr-action-btn {
    transition: none;
    animation: none;
  }

  .enhanced-card:hover {
    transform: none;
  }

  .action-button-enhanced:hover {
    transform: none;
  }
}
