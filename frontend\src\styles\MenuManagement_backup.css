/* Enhanced Menu Management Page Styles */

.menu-management-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 3rem 2rem;
  margin-bottom: 3rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.hero-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 3rem;
  position: relative;
  z-index: 1;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.hero-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: #fbbf24;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.hero-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.stat-icon {
  width: 1rem;
  height: 1rem;
  opacity: 0.8;
}

/* Enhanced Status Card */
.enhanced-status-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  padding: 2rem;
  min-width: 350px;
  flex-shrink: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.status-header {
  margin-bottom: 1.5rem;
}

.status-indicator-enhanced {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-icon-enhanced {
  width: 2rem;
  height: 2rem;
  padding: 0.5rem;
  border-radius: 12px;
  flex-shrink: 0;
}

.status-indicator-enhanced.active .status-icon-enhanced {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.status-indicator-enhanced:not(.active) .status-icon-enhanced {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.status-text-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-title {
  font-weight: 700;
  font-size: 1.125rem;
  color: #1f2937;
}

.status-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.status-actions {
  display: flex;
  justify-content: center;
}

.status-toggle-enhanced {
  padding: 1rem 2rem;
  border-radius: 16px;
  border: none;
  font-weight: 700;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
  min-width: 160px;
  justify-content: center;
}

.status-toggle-enhanced.active {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  box-shadow: 0 4px 16px rgba(251, 191, 36, 0.4);
}

.status-toggle-enhanced:not(.active) {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

.status-toggle-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.status-toggle-enhanced:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner-small {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

/* Enhanced Cards Grid */
.enhanced-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  align-items: stretch;
}

/* Enhanced Card Base */
.enhanced-card {
  background: white;
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.enhanced-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 0, 0, 0.1);
}

.enhanced-card:hover .card-glow {
  opacity: 1;
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.primary-card .card-glow {
  background: linear-gradient(90deg, #8b5cf6, #3b82f6);
}

.design-card .design-glow {
  background: linear-gradient(90deg, #f59e0b, #ef4444);
}

.share-card .share-glow {
  background: linear-gradient(90deg, #10b981, #06b6d4);
}

/* Card Header Enhanced */
.card-header-enhanced {
  padding: 2rem 2rem 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.card-icon-enhanced {
  width: 4rem;
  height: 4rem;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.card-icon-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  border-radius: inherit;
}

.card-icon-enhanced.edit-icon {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
}

.card-icon-enhanced.design-icon {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.card-icon-enhanced.share-icon {
  background: linear-gradient(135deg, #10b981, #06b6d4);
}

.icon-enhanced {
  width: 2rem;
  height: 2rem;
  color: white;
  position: relative;
  z-index: 1;
}

.card-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: white;
}

.primary-card .card-badge {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
}

.design-card .design-badge {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.share-card .share-badge {
  background: linear-gradient(135deg, #10b981, #06b6d4);
}
