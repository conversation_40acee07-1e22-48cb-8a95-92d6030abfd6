.product-editor {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.add-product-button {
    align-self: flex-start;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease;
}

.add-product-button:hover {
    background-color: #45a049;
}

.product-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 20px;
}

.product-form input,
.product-form textarea {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.2s ease;
}

.product-form input:focus,
.product-form textarea:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.product-form textarea {
    min-height: 80px;
    resize: vertical;
}

.product-form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.product-form-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.product-form-actions button[type="submit"] {
    background-color: #007bff;
    color: white;
}

.product-form-actions button[type="submit"]:hover {
    background-color: #0069d9;
}

.product-form-actions button[type="button"] {
    background-color: #6c757d;
    color: white;
}

.product-form-actions button[type="button"]:hover {
    background-color: #5a6268;
}

.product-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.product-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.product-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
}

.product-details {
    flex: 1;
}

.product-details h3 {
    margin: 0 0 8px 0;
    color: #2c3e50;
}

.product-details p {
    margin: 0 0 8px 0;
    color: #6c757d;
}

.price {
    font-weight: bold;
    color: #28a745;
}

.product-actions {
    display: flex;
    gap: 8px;
    align-items: flex-start;
}

.product-actions button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.product-actions button:first-child {
    background-color: #ffc107;
    color: #212529;
}

.product-actions button:first-child:hover {
    background-color: #e0a800;
}

.product-actions button:last-child {
    background-color: #dc3545;
    color: white;
}

.product-actions button:last-child:hover {
    background-color: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-item {
        flex-direction: column;
    }

    .product-image {
        width: 100%;
        height: 200px;
    }

    .product-actions {
        justify-content: flex-end;
    }
}

@media (max-width: 576px) {
    .product-form {
        padding: 15px;
    }

    .product-form-actions {
        flex-direction: column;
    }

    .product-form-actions button {
        width: 100%;
    }
} 