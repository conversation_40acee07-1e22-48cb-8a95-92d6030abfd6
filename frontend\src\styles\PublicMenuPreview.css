.preview-menu {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    max-height: 480px;
    font-size: 0.85rem;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.preview-header {
    padding: 10px 15px;
    text-align: center;
}

.preview-header h4 {
    margin: 0;
    font-size: 1.1rem;
}

.preview-categories {
    overflow-y: auto;
    padding: 15px;
    flex: 1;
}

/* Grid layout */
.preview-categories.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

/* List layout */
.preview-categories.list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.preview-category {
    margin-bottom: 10px;
}

.preview-categories.grid .preview-category {
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.preview-category h5 {
    margin: 0 0 8px 0;
    font-size: 1rem;
    position: relative;
}

.preview-categories.list .preview-category h5::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -3px;
    width: 30px;
    height: 2px;
    background-color: currentColor;
}

.preview-category-description {
    margin: 0 0 10px 0;
    font-size: 0.8rem;
    opacity: 0.8;
    font-style: italic;
}

.preview-products {
    display: grid;
    gap: 8px;
}

.preview-categories.grid .preview-products {
    grid-template-columns: 1fr;
}

.preview-categories.list .preview-products {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}

.preview-product {
    display: flex;
    flex-direction: column;
    border: 1px solid;
    border-radius: 5px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.preview-product:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.preview-product-image {
    height: 70px;
    overflow: hidden;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: rgba(0, 0, 0, 0.5);
}

.preview-product-details {
    padding: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.preview-product-details h6 {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
}

.preview-product-description {
    margin: 0 0 5px 0;
    font-size: 0.75rem;
    opacity: 0.8;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
}

.preview-product-price {
    font-weight: bold;
    font-size: 0.9rem;
    margin-top: auto;
}

.no-products, .no-categories, .preview-empty {
    color: rgba(0, 0, 0, 0.4);
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

/* Scrollbar styling */
.preview-categories::-webkit-scrollbar {
    width: 6px;
}

.preview-categories::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.preview-categories::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.preview-categories::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
} 