<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Login API Test</h1>
    <p>This page tests the login API endpoint directly.</p>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="password123">
    </div>
    
    <button id="loginBtn">Test Login</button>
    <div id="result">Results will appear here...</div>

    <script>
        document.getElementById('loginBtn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Sending login request...';
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                resultDiv.textContent += '\nAttempting to connect to: http://localhost:3001/api/auth/login';
                
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                resultDiv.textContent += `\nResponse status: ${response.status} ${response.statusText}`;
                
                try {
                    const data = await response.json();
                    resultDiv.textContent += '\nResponse data: ' + JSON.stringify(data, null, 2);
                } catch (e) {
                    resultDiv.textContent += '\nError parsing JSON: ' + e.message;
                }
            } catch (error) {
                resultDiv.textContent += '\nFetch error: ' + error.message;
            }
        });
    </script>
</body>
</html> 