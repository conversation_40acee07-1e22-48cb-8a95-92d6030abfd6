<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication System</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .container { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        input { padding: 10px; width: 300px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔐 Authentication System Test</h1>
    
    <div class="container">
        <h2>Register New User</h2>
        <input type="email" id="registerEmail" placeholder="Email" />
        <input type="password" id="registerPassword" placeholder="Password" />
        <input type="password" id="confirmPassword" placeholder="Confirm Password" />
        <br>
        <button onclick="testRegister()">Register</button>
    </div>

    <div class="container">
        <h2>Login User</h2>
        <input type="email" id="loginEmail" placeholder="Email" />
        <input type="password" id="loginPassword" placeholder="Password" />
        <br>
        <button onclick="testLogin()">Login</button>
    </div>

    <div class="container">
        <h2>Test Protected Routes</h2>
        <button onclick="testProfile()">Get Profile (/auth/me)</button>
        <button onclick="testProtectedRoute()">Test Protected Route</button>
        <button onclick="testDashboard()">Test Dashboard</button>
        <button onclick="testLogout()">Logout</button>
    </div>

    <div id="output"></div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let authToken = localStorage.getItem('authToken');

        function showMessage(message, type = 'info') {
            const output = document.getElementById('output');
            output.innerHTML = `<div class="${type}">${message}</div>` + output.innerHTML;
        }

        function showResponse(title, response) {
            const output = document.getElementById('output');
            output.innerHTML = `
                <div class="container">
                    <h3>${title}</h3>
                    <pre>${JSON.stringify(response, null, 2)}</pre>
                </div>
            ` + output.innerHTML;
        }

        async function testRegister() {
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password, confirmPassword }),
                });

                const data = await response.json();
                
                if (data.success) {
                    authToken = data.token;
                    localStorage.setItem('authToken', authToken);
                    showMessage('✅ Registration successful!', 'success');
                } else {
                    showMessage('❌ Registration failed: ' + data.message, 'error');
                }

                showResponse('Registration Response', data);
            } catch (error) {
                showMessage('❌ Registration error: ' + error.message, 'error');
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password }),
                });

                const data = await response.json();
                
                if (data.success) {
                    authToken = data.token;
                    localStorage.setItem('authToken', authToken);
                    showMessage('✅ Login successful!', 'success');
                } else {
                    showMessage('❌ Login failed: ' + data.message, 'error');
                }

                showResponse('Login Response', data);
            } catch (error) {
                showMessage('❌ Login error: ' + error.message, 'error');
            }
        }

        async function testProfile() {
            if (!authToken) {
                showMessage('❌ Please login first', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                    credentials: 'include',
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage('✅ Profile retrieved successfully!', 'success');
                } else {
                    showMessage('❌ Profile failed: ' + data.message, 'error');
                }

                showResponse('Profile Response', data);
            } catch (error) {
                showMessage('❌ Profile error: ' + error.message, 'error');
            }
        }

        async function testProtectedRoute() {
            if (!authToken) {
                showMessage('❌ Please login first', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/protected/profile`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                    credentials: 'include',
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage('✅ Protected route accessed successfully!', 'success');
                } else {
                    showMessage('❌ Protected route failed: ' + data.message, 'error');
                }

                showResponse('Protected Route Response', data);
            } catch (error) {
                showMessage('❌ Protected route error: ' + error.message, 'error');
            }
        }

        async function testDashboard() {
            if (!authToken) {
                showMessage('❌ Please login first', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/protected/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                    credentials: 'include',
                });

                const data = await response.json();
                
                if (data.success) {
                    showMessage('✅ Dashboard accessed successfully!', 'success');
                } else {
                    showMessage('❌ Dashboard failed: ' + data.message, 'error');
                }

                showResponse('Dashboard Response', data);
            } catch (error) {
                showMessage('❌ Dashboard error: ' + error.message, 'error');
            }
        }

        async function testLogout() {
            try {
                const response = await fetch(`${API_BASE}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                    credentials: 'include',
                });

                const data = await response.json();
                
                if (data.success) {
                    authToken = null;
                    localStorage.removeItem('authToken');
                    showMessage('✅ Logout successful!', 'success');
                } else {
                    showMessage('❌ Logout failed: ' + data.message, 'error');
                }

                showResponse('Logout Response', data);
            } catch (error) {
                showMessage('❌ Logout error: ' + error.message, 'error');
            }
        }

        // Show current auth status on load
        window.addEventListener('load', () => {
            if (authToken) {
                showMessage('🔑 Auth token found in localStorage', 'info');
            } else {
                showMessage('🔓 No auth token found', 'info');
            }
        });
    </script>
</body>
</html>
